Complete Storage Policy Definitions
For KYC Bucket (Private)
Policy Name: Users can manage own KYC files
Allowed Operations: SELECT, INSERT, UPDATE, DELETE
Target Roles: authenticated
Policy Definition:
bucket_id = 'kyc' AND auth.uid()::text = (storage.foldername(name))[1]

For Products Bucket (Public)
Policy 1:

Policy Name: Public read access for products
Allowed Operations: SELECT
Target Roles: public
Policy Definition:
bucket_id = 'products'

Policy 2:

Policy Name: Authenticated upload for products
Allowed Operations: INSERT, UPDATE, DELETE
Target Roles: authenticated
Policy Definition:
bucket_id = 'products'

For Categories Bucket (Public)
Policy 1:

Policy Name: Public read access for categories
Allowed Operations: SELECT
Target Roles: public
Policy Definition:
bucket_id = 'categories'

Policy 2:

Policy Name: Authenticated upload for categories
Allowed Operations: INSERT, UPDATE, DELETE
Target Roles: authenticated
Policy Definition:
bucket_id = 'categories'

For Banners Bucket (Public)
Policy 1:

Policy Name: Public read access for banners
Allowed Operations: SELECT
Target Roles: public
Policy Definition:
bucket_id = 'banners'

Policy 2:

Policy Name: Authenticated upload for banners
Allowed Operations: INSERT, UPDATE, DELETE
Target Roles: authenticated
Policy Definition:
bucket_id = 'banners'