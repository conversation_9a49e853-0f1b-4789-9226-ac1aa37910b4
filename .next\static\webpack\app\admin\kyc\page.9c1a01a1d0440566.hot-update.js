"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/kyc/page",{

/***/ "(app-pages-browser)/./src/app/admin/kyc/page.tsx":
/*!************************************!*\
  !*** ./src/app/admin/kyc/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ KYCPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_layout_admin_layout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/admin-layout */ \"(app-pages-browser)/./src/components/layout/admin-layout.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_admin_kyc_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/admin/kyc-image */ \"(app-pages-browser)/./src/components/admin/kyc-image.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction KYCPage() {\n    var _selectedDoc_profiles, _selectedDoc_profiles1;\n    _s();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('pending');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedDoc, setSelectedDoc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rejectionReason, setRejectionReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"KYCPage.useEffect\": ()=>{\n            fetchKYCDocuments();\n        }\n    }[\"KYCPage.useEffect\"], [\n        activeTab\n    ]);\n    const fetchKYCDocuments = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/admin/kyc?status=\".concat(activeTab));\n            if (response.ok) {\n                const data = await response.json();\n                setDocuments(data.documents);\n            } else {\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                    title: \"Error\",\n                    description: \"Failed to fetch KYC documents\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to fetch KYC documents\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleVerificationAction = async (documentId, action, reason)=>{\n        try {\n            const response = await fetch(\"/api/admin/kyc/\".concat(documentId), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action,\n                    rejection_reason: reason\n                })\n            });\n            if (response.ok) {\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                    title: \"Success\",\n                    description: \"KYC \".concat(action === 'approve' ? 'approved' : 'rejected', \" successfully\")\n                });\n                fetchKYCDocuments();\n                setSelectedDoc(null);\n                setRejectionReason('');\n            } else {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Failed to \".concat(action, \" KYC\"));\n            }\n        } catch (error) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: error instanceof Error ? error.message : \"Failed to \".concat(action, \" KYC\"),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const openDocumentViewer = (doc)=>{\n        setSelectedDoc(doc);\n    };\n    const closeDocumentViewer = ()=>{\n        setSelectedDoc(null);\n        setRejectionReason('');\n    };\n    const filteredDocuments = documents.filter((doc)=>{\n        var _doc_profiles_full_name, _doc_profiles, _doc_profiles_email, _doc_profiles1, _doc_profiles_phone, _doc_profiles2, _doc_aadhaar_number;\n        return ((_doc_profiles = doc.profiles) === null || _doc_profiles === void 0 ? void 0 : (_doc_profiles_full_name = _doc_profiles.full_name) === null || _doc_profiles_full_name === void 0 ? void 0 : _doc_profiles_full_name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_doc_profiles1 = doc.profiles) === null || _doc_profiles1 === void 0 ? void 0 : (_doc_profiles_email = _doc_profiles1.email) === null || _doc_profiles_email === void 0 ? void 0 : _doc_profiles_email.toLowerCase().includes(searchTerm.toLowerCase())) || ((_doc_profiles2 = doc.profiles) === null || _doc_profiles2 === void 0 ? void 0 : (_doc_profiles_phone = _doc_profiles2.phone) === null || _doc_profiles_phone === void 0 ? void 0 : _doc_profiles_phone.includes(searchTerm)) || ((_doc_aadhaar_number = doc.aadhaar_number) === null || _doc_aadhaar_number === void 0 ? void 0 : _doc_aadhaar_number.includes(searchTerm));\n    });\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'verified':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-5 w-5 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 16\n                }, this);\n            case 'rejected':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-5 w-5 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const colors = {\n            pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',\n            verified: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',\n            rejected: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(colors[status]),\n            children: [\n                getStatusIcon(status),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-1 capitalize\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_admin_layout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold\",\n                            children: \"KYC Verification\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Review and approve seller identity documents\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1 bg-muted p-1 rounded-lg w-fit\",\n                    children: [\n                        'pending',\n                        'verified',\n                        'rejected'\n                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: activeTab === tab ? 'default' : 'ghost',\n                            size: \"sm\",\n                            onClick: ()=>setActiveTab(tab),\n                            className: \"capitalize\",\n                            children: [\n                                tab,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 bg-background/20 text-xs px-1.5 py-0.5 rounded\",\n                                    children: documents.length\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, tab, true, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"absolute left-3 top-3 h-4 w-4 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    placeholder: \"Search by name, email, phone, or Aadhaar number...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"pl-10\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this),\n                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        ...Array(6)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"animate-pulse\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-3/4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 17\n                            }, this)\n                        }, i, false, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 11\n                }, this) : filteredDocuments.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: filteredDocuments.map((doc)=>{\n                        var _doc_profiles, _doc_profiles1;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: ((_doc_profiles = doc.profiles) === null || _doc_profiles === void 0 ? void 0 : _doc_profiles.full_name) || 'Unnamed User'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: (_doc_profiles1 = doc.profiles) === null || _doc_profiles1 === void 0 ? void 0 : _doc_profiles1.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        getStatusBadge(doc.verification_status)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"User ID: \",\n                                                                        doc.user_id.slice(0, 8),\n                                                                        \"...\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: doc.aadhaar_number ? \"****-****-\".concat(doc.aadhaar_number.slice(-4)) : 'No Aadhaar number'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 246,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: doc.aadhaar_url && doc.selfie_url ? 'Both documents uploaded' : 'Incomplete documents'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Submitted: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(doc.created_at)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                doc.verified_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Verified: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(doc.verified_at)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 23\n                                                }, this),\n                                                doc.rejection_reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-md\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-800 dark:text-red-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Rejection Reason:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            \" \",\n                                                            doc.rejection_reason\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-2 lg:ml-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>openDocumentViewer(doc),\n                                                className: \"w-full sm:w-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"Review Documents\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 17\n                            }, this)\n                        }, doc.id, false, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"flex flex-col items-center justify-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-12 w-12 text-muted-foreground mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"No KYC Documents Found\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-center\",\n                                children: searchTerm ? \"No documents match your search.\" : \"No \".concat(activeTab, \" KYC documents at the moment.\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this),\n                selectedDoc && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-background rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold\",\n                                                    children: [\n                                                        \"KYC Documents - \",\n                                                        (_selectedDoc_profiles = selectedDoc.profiles) === null || _selectedDoc_profiles === void 0 ? void 0 : _selectedDoc_profiles.full_name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: (_selectedDoc_profiles1 = selectedDoc.profiles) === null || _selectedDoc_profiles1 === void 0 ? void 0 : _selectedDoc_profiles1.email\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            onClick: closeDocumentViewer,\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium mb-3\",\n                                                    children: \"Aadhaar Card\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 21\n                                                }, this),\n                                                selectedDoc.aadhaar_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_kyc_image__WEBPACK_IMPORTED_MODULE_8__.KYCImage, {\n                                                    path: selectedDoc.aadhaar_url,\n                                                    alt: \"Aadhaar Card\",\n                                                    width: 400,\n                                                    height: 250\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-2 border-dashed border-muted rounded-lg p-8 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-8 w-8 text-muted-foreground mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"No Aadhaar document uploaded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium mb-3\",\n                                                    children: \"Selfie\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 21\n                                                }, this),\n                                                selectedDoc.selfie_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_kyc_image__WEBPACK_IMPORTED_MODULE_8__.KYCImage, {\n                                                    path: selectedDoc.selfie_url,\n                                                    alt: \"Selfie\",\n                                                    width: 400,\n                                                    height: 250\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-2 border-dashed border-muted rounded-lg p-8 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-8 w-8 text-muted-foreground mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"No selfie uploaded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 17\n                                }, this),\n                                selectedDoc.aadhaar_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 p-4 bg-muted rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium mb-2\",\n                                            children: \"Aadhaar Number\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-mono text-lg\",\n                                            children: selectedDoc.aadhaar_number\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 19\n                                }, this),\n                                selectedDoc.verification_status === 'pending' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Rejection Reason (if rejecting)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    placeholder: \"Enter reason for rejection...\",\n                                                    value: rejectionReason,\n                                                    onChange: (e)=>setRejectionReason(e.target.value),\n                                                    className: \"mt-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: ()=>handleVerificationAction(selectedDoc.id, 'approve'),\n                                                    className: \"bg-green-600 hover:bg-green-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Approve KYC\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"destructive\",\n                                                    onClick: ()=>handleVerificationAction(selectedDoc.id, 'reject', rejectionReason),\n                                                    disabled: !rejectionReason.trim(),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Reject KYC\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(KYCPage, \"eHaR2xYH0VERwt/2PtPfhXyL5hw=\");\n_c = KYCPage;\nvar _c;\n$RefreshReg$(_c, \"KYCPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/kyc/page.tsx\n"));

/***/ })

});