"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/kyc/page",{

/***/ "(app-pages-browser)/./src/app/admin/kyc/page.tsx":
/*!************************************!*\
  !*** ./src/app/admin/kyc/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ KYCPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_layout_admin_layout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/admin-layout */ \"(app-pages-browser)/./src/components/layout/admin-layout.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_admin_kyc_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/admin/kyc-image */ \"(app-pages-browser)/./src/components/admin/kyc-image.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction KYCPage() {\n    var _selectedDoc_profiles, _selectedDoc_profiles1;\n    _s();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('pending');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedDoc, setSelectedDoc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rejectionReason, setRejectionReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"KYCPage.useEffect\": ()=>{\n            fetchKYCDocuments();\n        }\n    }[\"KYCPage.useEffect\"], [\n        activeTab\n    ]);\n    const fetchKYCDocuments = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/admin/kyc?status=\".concat(activeTab));\n            if (response.ok) {\n                const data = await response.json();\n                setDocuments(data.documents);\n            } else {\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                    title: \"Error\",\n                    description: \"Failed to fetch KYC documents\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to fetch KYC documents\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleVerificationAction = async (documentId, action, reason)=>{\n        try {\n            const response = await fetch(\"/api/admin/kyc/\".concat(documentId), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action,\n                    rejection_reason: reason\n                })\n            });\n            if (response.ok) {\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                    title: \"Success\",\n                    description: \"KYC \".concat(action === 'approve' ? 'approved' : 'rejected', \" successfully\")\n                });\n                fetchKYCDocuments();\n                setSelectedDoc(null);\n                setRejectionReason('');\n            } else {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Failed to \".concat(action, \" KYC\"));\n            }\n        } catch (error) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: error instanceof Error ? error.message : \"Failed to \".concat(action, \" KYC\"),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const openDocumentViewer = (doc)=>{\n        setSelectedDoc(doc);\n    };\n    const closeDocumentViewer = ()=>{\n        setSelectedDoc(null);\n        setRejectionReason('');\n    };\n    const filteredDocuments = documents.filter((doc)=>{\n        var _doc_profiles_full_name, _doc_profiles, _doc_profiles_email, _doc_profiles1, _doc_profiles_phone, _doc_profiles2, _doc_aadhaar_number;\n        return ((_doc_profiles = doc.profiles) === null || _doc_profiles === void 0 ? void 0 : (_doc_profiles_full_name = _doc_profiles.full_name) === null || _doc_profiles_full_name === void 0 ? void 0 : _doc_profiles_full_name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_doc_profiles1 = doc.profiles) === null || _doc_profiles1 === void 0 ? void 0 : (_doc_profiles_email = _doc_profiles1.email) === null || _doc_profiles_email === void 0 ? void 0 : _doc_profiles_email.toLowerCase().includes(searchTerm.toLowerCase())) || ((_doc_profiles2 = doc.profiles) === null || _doc_profiles2 === void 0 ? void 0 : (_doc_profiles_phone = _doc_profiles2.phone) === null || _doc_profiles_phone === void 0 ? void 0 : _doc_profiles_phone.includes(searchTerm)) || ((_doc_aadhaar_number = doc.aadhaar_number) === null || _doc_aadhaar_number === void 0 ? void 0 : _doc_aadhaar_number.includes(searchTerm));\n    });\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'verified':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-5 w-5 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 16\n                }, this);\n            case 'rejected':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-5 w-5 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 16\n                }, this);\n            case 'in_review':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-5 w-5 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const colors = {\n            pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',\n            in_review: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',\n            verified: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',\n            rejected: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(colors[status]),\n            children: [\n                getStatusIcon(status),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-1 capitalize\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_admin_layout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold\",\n                            children: \"KYC Verification\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Review and approve seller identity documents\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1 bg-muted p-1 rounded-lg w-fit\",\n                    children: [\n                        'pending',\n                        'in_review',\n                        'verified',\n                        'rejected'\n                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: activeTab === tab ? 'default' : 'ghost',\n                            size: \"sm\",\n                            onClick: ()=>setActiveTab(tab),\n                            className: \"capitalize\",\n                            children: [\n                                tab === 'in_review' ? 'In Review' : tab,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 bg-background/20 text-xs px-1.5 py-0.5 rounded\",\n                                    children: documents.length\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, tab, true, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"absolute left-3 top-3 h-4 w-4 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    placeholder: \"Search by name, email, phone, or Aadhaar number...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"pl-10\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this),\n                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        ...Array(6)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"animate-pulse\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-3/4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 17\n                            }, this)\n                        }, i, false, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 11\n                }, this) : filteredDocuments.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: filteredDocuments.map((doc)=>{\n                        var _doc_profiles, _doc_profiles1;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: ((_doc_profiles = doc.profiles) === null || _doc_profiles === void 0 ? void 0 : _doc_profiles.full_name) || 'Unnamed User'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: (_doc_profiles1 = doc.profiles) === null || _doc_profiles1 === void 0 ? void 0 : _doc_profiles1.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        getStatusBadge(doc.verification_status)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"User ID: \",\n                                                                        doc.user_id.slice(0, 8),\n                                                                        \"...\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 241,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: doc.aadhaar_number ? \"****-****-\".concat(doc.aadhaar_number.slice(-4)) : 'No Aadhaar number'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: doc.aadhaar_url && doc.selfie_url ? 'Both documents uploaded' : 'Incomplete documents'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Submitted: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(doc.created_at)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                doc.verified_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Verified: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(doc.verified_at)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 23\n                                                }, this),\n                                                doc.rejection_reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-md\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-800 dark:text-red-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Rejection Reason:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            \" \",\n                                                            doc.rejection_reason\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-2 lg:ml-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>openDocumentViewer(doc),\n                                                className: \"w-full sm:w-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"Review Documents\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 17\n                            }, this)\n                        }, doc.id, false, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"flex flex-col items-center justify-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-12 w-12 text-muted-foreground mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"No KYC Documents Found\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-center\",\n                                children: searchTerm ? \"No documents match your search.\" : \"No \".concat(activeTab, \" KYC documents at the moment.\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 11\n                }, this),\n                selectedDoc && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-background rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold\",\n                                                    children: [\n                                                        \"KYC Documents - \",\n                                                        (_selectedDoc_profiles = selectedDoc.profiles) === null || _selectedDoc_profiles === void 0 ? void 0 : _selectedDoc_profiles.full_name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: (_selectedDoc_profiles1 = selectedDoc.profiles) === null || _selectedDoc_profiles1 === void 0 ? void 0 : _selectedDoc_profiles1.email\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            onClick: closeDocumentViewer,\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium mb-3\",\n                                                    children: \"Aadhaar Card\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 21\n                                                }, this),\n                                                selectedDoc.aadhaar_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_kyc_image__WEBPACK_IMPORTED_MODULE_8__.KYCImage, {\n                                                    path: selectedDoc.aadhaar_url,\n                                                    alt: \"Aadhaar Card\",\n                                                    width: 400,\n                                                    height: 250\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-2 border-dashed border-muted rounded-lg p-8 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-8 w-8 text-muted-foreground mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"No Aadhaar document uploaded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium mb-3\",\n                                                    children: \"Selfie\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 21\n                                                }, this),\n                                                selectedDoc.selfie_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_kyc_image__WEBPACK_IMPORTED_MODULE_8__.KYCImage, {\n                                                    path: selectedDoc.selfie_url,\n                                                    alt: \"Selfie\",\n                                                    width: 400,\n                                                    height: 250\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-2 border-dashed border-muted rounded-lg p-8 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-8 w-8 text-muted-foreground mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"No selfie uploaded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 17\n                                }, this),\n                                selectedDoc.aadhaar_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 p-4 bg-muted rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium mb-2\",\n                                            children: \"Aadhaar Number\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-mono text-lg\",\n                                            children: selectedDoc.aadhaar_number\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 19\n                                }, this),\n                                (selectedDoc.verification_status === 'pending' || selectedDoc.verification_status === 'in_review') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Rejection Reason (if rejecting)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    placeholder: \"Enter reason for rejection...\",\n                                                    value: rejectionReason,\n                                                    onChange: (e)=>setRejectionReason(e.target.value),\n                                                    className: \"mt-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: ()=>handleVerificationAction(selectedDoc.id, 'approve'),\n                                                    className: \"bg-green-600 hover:bg-green-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Approve KYC\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"destructive\",\n                                                    onClick: ()=>handleVerificationAction(selectedDoc.id, 'reject', rejectionReason),\n                                                    disabled: !rejectionReason.trim(),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Reject KYC\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n_s(KYCPage, \"eHaR2xYH0VERwt/2PtPfhXyL5hw=\");\n_c = KYCPage;\nvar _c;\n$RefreshReg$(_c, \"KYCPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/kyc/page.tsx\n"));

/***/ })

});