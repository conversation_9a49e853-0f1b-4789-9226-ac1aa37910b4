"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/kyc/page",{

/***/ "(app-pages-browser)/./src/app/admin/kyc/page.tsx":
/*!************************************!*\
  !*** ./src/app/admin/kyc/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ KYCPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_layout_admin_layout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/admin-layout */ \"(app-pages-browser)/./src/components/layout/admin-layout.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_admin_kyc_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/admin/kyc-image */ \"(app-pages-browser)/./src/components/admin/kyc-image.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction KYCPage() {\n    var _selectedDoc_profiles, _selectedDoc_profiles1;\n    _s();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('pending');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedDoc, setSelectedDoc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rejectionReason, setRejectionReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"KYCPage.useEffect\": ()=>{\n            fetchKYCDocuments();\n        }\n    }[\"KYCPage.useEffect\"], [\n        activeTab\n    ]);\n    const fetchKYCDocuments = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/admin/kyc?status=\".concat(activeTab));\n            if (response.ok) {\n                const data = await response.json();\n                setDocuments(data.documents);\n            } else {\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                    title: \"Error\",\n                    description: \"Failed to fetch KYC documents\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to fetch KYC documents\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleVerificationAction = async (documentId, action, reason)=>{\n        try {\n            const response = await fetch(\"/api/admin/kyc/\".concat(documentId), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action,\n                    rejection_reason: reason\n                })\n            });\n            if (response.ok) {\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                    title: \"Success\",\n                    description: \"KYC \".concat(action === 'approve' ? 'approved' : 'rejected', \" successfully\")\n                });\n                fetchKYCDocuments();\n                setSelectedDoc(null);\n                setRejectionReason('');\n            } else {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Failed to \".concat(action, \" KYC\"));\n            }\n        } catch (error) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: error instanceof Error ? error.message : \"Failed to \".concat(action, \" KYC\"),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const openDocumentViewer = (doc)=>{\n        setSelectedDoc(doc);\n    };\n    const closeDocumentViewer = ()=>{\n        setSelectedDoc(null);\n        setRejectionReason('');\n    };\n    const filteredDocuments = documents.filter((doc)=>{\n        var _doc_profiles_full_name, _doc_profiles, _doc_profiles_email, _doc_profiles1, _doc_profiles_phone, _doc_profiles2, _doc_aadhaar_number;\n        if (!searchTerm.trim()) return true;\n        const searchLower = searchTerm.toLowerCase();\n        return ((_doc_profiles = doc.profiles) === null || _doc_profiles === void 0 ? void 0 : (_doc_profiles_full_name = _doc_profiles.full_name) === null || _doc_profiles_full_name === void 0 ? void 0 : _doc_profiles_full_name.toLowerCase().includes(searchLower)) || ((_doc_profiles1 = doc.profiles) === null || _doc_profiles1 === void 0 ? void 0 : (_doc_profiles_email = _doc_profiles1.email) === null || _doc_profiles_email === void 0 ? void 0 : _doc_profiles_email.toLowerCase().includes(searchLower)) || ((_doc_profiles2 = doc.profiles) === null || _doc_profiles2 === void 0 ? void 0 : (_doc_profiles_phone = _doc_profiles2.phone) === null || _doc_profiles_phone === void 0 ? void 0 : _doc_profiles_phone.includes(searchTerm)) || ((_doc_aadhaar_number = doc.aadhaar_number) === null || _doc_aadhaar_number === void 0 ? void 0 : _doc_aadhaar_number.includes(searchTerm));\n    });\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'verified':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-5 w-5 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 16\n                }, this);\n            case 'rejected':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-5 w-5 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 16\n                }, this);\n            case 'in_review':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-5 w-5 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const colors = {\n            pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',\n            in_review: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',\n            verified: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',\n            rejected: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(colors[status]),\n            children: [\n                getStatusIcon(status),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-1 capitalize\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_admin_layout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold\",\n                            children: \"KYC Verification\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Review and approve seller identity documents\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1 bg-muted p-1 rounded-lg w-fit\",\n                    children: [\n                        'pending',\n                        'in_review',\n                        'verified',\n                        'rejected'\n                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: activeTab === tab ? 'default' : 'ghost',\n                            size: \"sm\",\n                            onClick: ()=>setActiveTab(tab),\n                            className: \"capitalize\",\n                            children: [\n                                tab === 'in_review' ? 'In Review' : tab,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 bg-background/20 text-xs px-1.5 py-0.5 rounded\",\n                                    children: documents.length\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, tab, true, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"absolute left-3 top-3 h-4 w-4 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    placeholder: \"Search by name, email, phone, or Aadhaar number...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"pl-10\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this),\n                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        ...Array(6)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"animate-pulse\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-3/4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 17\n                            }, this)\n                        }, i, false, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 11\n                }, this) : filteredDocuments.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: filteredDocuments.map((doc)=>{\n                        var _doc_profiles, _doc_profiles1;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: ((_doc_profiles = doc.profiles) === null || _doc_profiles === void 0 ? void 0 : _doc_profiles.full_name) || 'Unnamed User'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: (_doc_profiles1 = doc.profiles) === null || _doc_profiles1 === void 0 ? void 0 : _doc_profiles1.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        getStatusBadge(doc.verification_status)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"User ID: \",\n                                                                        doc.user_id.slice(0, 8),\n                                                                        \"...\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 243,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 246,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: doc.aadhaar_number ? \"****-****-\".concat(doc.aadhaar_number.slice(-4)) : 'No Aadhaar number'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: doc.aadhaar_url && doc.selfie_url ? 'Both documents uploaded' : 'Incomplete documents'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Submitted: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(doc.created_at)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                doc.verified_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Verified: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(doc.verified_at)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 23\n                                                }, this),\n                                                doc.rejection_reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-md\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-800 dark:text-red-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Rejection Reason:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            \" \",\n                                                            doc.rejection_reason\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-2 lg:ml-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>openDocumentViewer(doc),\n                                                className: \"w-full sm:w-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"Review Documents\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 17\n                            }, this)\n                        }, doc.id, false, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"flex flex-col items-center justify-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-12 w-12 text-muted-foreground mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"No KYC Documents Found\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-center\",\n                                children: searchTerm ? \"No documents match your search.\" : \"No \".concat(activeTab, \" KYC documents at the moment.\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 11\n                }, this),\n                selectedDoc && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-background rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold\",\n                                                    children: [\n                                                        \"KYC Documents - \",\n                                                        (_selectedDoc_profiles = selectedDoc.profiles) === null || _selectedDoc_profiles === void 0 ? void 0 : _selectedDoc_profiles.full_name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: (_selectedDoc_profiles1 = selectedDoc.profiles) === null || _selectedDoc_profiles1 === void 0 ? void 0 : _selectedDoc_profiles1.email\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            onClick: closeDocumentViewer,\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium mb-3\",\n                                                    children: \"Aadhaar Card\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 21\n                                                }, this),\n                                                selectedDoc.aadhaar_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_kyc_image__WEBPACK_IMPORTED_MODULE_8__.KYCImage, {\n                                                    path: selectedDoc.aadhaar_url,\n                                                    alt: \"Aadhaar Card\",\n                                                    width: 400,\n                                                    height: 250\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-2 border-dashed border-muted rounded-lg p-8 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-8 w-8 text-muted-foreground mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"No Aadhaar document uploaded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium mb-3\",\n                                                    children: \"Selfie\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 21\n                                                }, this),\n                                                selectedDoc.selfie_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_kyc_image__WEBPACK_IMPORTED_MODULE_8__.KYCImage, {\n                                                    path: selectedDoc.selfie_url,\n                                                    alt: \"Selfie\",\n                                                    width: 400,\n                                                    height: 250\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-2 border-dashed border-muted rounded-lg p-8 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-8 w-8 text-muted-foreground mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"No selfie uploaded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 17\n                                }, this),\n                                selectedDoc.aadhaar_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 p-4 bg-muted rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium mb-2\",\n                                            children: \"Aadhaar Number\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-mono text-lg\",\n                                            children: selectedDoc.aadhaar_number\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 19\n                                }, this),\n                                (selectedDoc.verification_status === 'pending' || selectedDoc.verification_status === 'in_review') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Rejection Reason (if rejecting)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    placeholder: \"Enter reason for rejection...\",\n                                                    value: rejectionReason,\n                                                    onChange: (e)=>setRejectionReason(e.target.value),\n                                                    className: \"mt-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: ()=>handleVerificationAction(selectedDoc.id, 'approve'),\n                                                    className: \"bg-green-600 hover:bg-green-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Approve KYC\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"destructive\",\n                                                    onClick: ()=>handleVerificationAction(selectedDoc.id, 'reject', rejectionReason),\n                                                    disabled: !rejectionReason.trim(),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Reject KYC\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n_s(KYCPage, \"eHaR2xYH0VERwt/2PtPfhXyL5hw=\");\n_c = KYCPage;\nvar _c;\n$RefreshReg$(_c, \"KYCPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/kyc/page.tsx\n"));

/***/ })

});