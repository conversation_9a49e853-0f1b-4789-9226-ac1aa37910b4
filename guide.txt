==============================
GUIDE.TXT – Admin Panel for GurToy
==============================

🧾 OVERVIEW
-----------
This admin panel is used to manage all core aspects of the GurToy seller-buyer system. It is designed in a clean, elegant black-and-white theme, with smooth interactions, minimal colors, sharp typography, and strong contrast — ideal for productivity and visual clarity.

===========================
🛠 TECH STACK
===========================
- Framework: Next.js 14 (App Router, TypeScript)
- Styling: Tailwind CSS (dark + light mode with black & white theme)
- UI Components: ShadCN UI
- Auth & DB: Supabase
- File Storage: Supabase Storage
- State & Forms: React Hooks, Zustand/Context, React Hook Form
- Animation: Framer Motion

===========================
🎨 DESIGN THEME
===========================
- Base Theme: **Black & White**
- Mode: Light and Dark (toggleable)
- Text Color: `#000` or `#fff`
- Accent: Subtle grey (`#1A1A1A`, `#F5F5F5`)
- Button Focus Ring: White in dark, Black in light
- Font: `Inter`, `DM Sans`, or `Poppins`

Use minimal icons and grayscale shadows for cards.

===========================
🔐 ADMIN LOGIN
===========================
- Email: `<EMAIL>`
- Password: `Toys123@`
- Authentication via Supabase `admin_users` table
- Use secure cookie/session or Supabase Auth (if preferred)
- Redirect to `/admin/dashboard` after login

===========================
📂 ROUTES & FEATURES
===========================

1️⃣ `/admin/login`
- Simple login form
- Background: Black, Form: White card with black text

2️⃣ `/admin/dashboard`
- Overview cards (white cards on black background)
  - Total Products
  - Total Sellers / Buyers
  - Pending Orders / Verifications
  - Quick Actions (buttons)

3️⃣ `/admin/products`
- List View: Table of products
- Create/Edit Product:
  - Name, Price, Description
  - Gallery Upload (Supabase Storage `products`)
  - Category selection
  - Stock Quantity, Tags

4️⃣ `/admin/categories`
- Add/Edit/Delete categories
- Upload icons to `categories` bucket
- List with table and delete/edit actions

5️⃣ `/admin/users/sellers`
- List all sellers
- View KYC status, documents (from `kyc` bucket)
- Approve/Reject KYC manually
- Suspend/Activate seller
- See total earnings, referrals

6️⃣ `/admin/users/buyers`
- List buyers
- View buyer info + past orders

7️⃣ `/admin/orders`
- Filter by: status, date, seller
- Change order status
- Add tracking code
- View payment (Razorpay ID), refund button (placeholder for now)

8️⃣ `/admin/wallet`
- View seller wallets
- Approve/reject withdrawals manually
- Adjust balance manually (form)

9️⃣ `/admin/banners`
- Upload banners for homepage
- Schedule, activate/deactivate
- Store in `banners` bucket

🔟 `/admin/settings`
- Support Info:
  - Email: `<EMAIL>`
  - Phone: `9041855598`, `9041844498`
  - Address: `6/7 Char Khamba Rd, Model Town, Ludhiana, Punjab 141002`
- Set Razorpay key (placeholder for now)
- Toggle features like: KYC required, referral on/off

===========================
📦 SUPABASE STORAGE BUCKETS
===========================
- `products/` → product images (public)
- `categories/` → category icons
- `kyc/` → Aadhaar & Selfie (private)
- `banners/` → homepage banners

===========================
📑 SUPABASE TABLES
===========================
- `admin_users` → admin auth
- `products`, `categories`, `banners`
- `profiles` → user info
- `seller_products`, `sales`, `orders`
- `verification_documents`
- `wallet_transactions`

===========================
📱 RESPONSIVE DESIGN
===========================
- Fully responsive for mobile/tablet
- Navigation collapses to sidebar drawer
- Use grid/list toggles for tables
- Responsive modals/forms

===========================
✨ ADDITIONAL TIPS
===========================
- Use ShadCN’s built-in dark/light theme switcher
- Create `AdminLayout.tsx` to wrap all admin pages
- Use a `withAdminAuth()` HOC to restrict routes
- Use Supabase RLS for secure access

===========================
✅ DEPLOYMENT
===========================
- Build with `next build`
- Deploy on Vercel or similar
- Set ENV:
  - SUPABASE_URL
  - SUPABASE_ANON_KEY
  - SUPABASE_SERVICE_ROLE_KEY (for secure Edge Functions)
  - RAZORPAY_TEST_KEY / SECRET

