import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import jwt from 'jsonwebtoken'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const token = request.cookies.get('admin-token')?.value
    if (!token) {
      return NextResponse.json({ message: 'Not authenticated' }, { status: 401 })
    }

    jwt.verify(token, process.env.ADMIN_JWT_SECRET || 'your-secret-key')

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') || 'pending'
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Fetch KYC documents with user profiles
    const { data: documents, error } = await supabaseAdmin
      .from('verification_documents')
      .select(`
        id,
        user_id,
        aadhaar_url,
        selfie_url,
        aadhaar_number,
        verification_status,
        rejection_reason,
        verified_at,
        verified_by,
        created_at,
        updated_at,
        profiles!inner (
          full_name,
          email,
          phone
        )
      `)
      .eq('verification_status', status)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching KYC documents:', error)
      return NextResponse.json(
        { message: 'Failed to fetch KYC documents' },
        { status: 500 }
      )
    }

    return NextResponse.json({ documents: documents || [] })

  } catch (error) {
    console.error('KYC API error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}