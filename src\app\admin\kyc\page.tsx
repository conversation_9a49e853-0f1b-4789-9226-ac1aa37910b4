'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import AdminLayout from '@/components/layout/admin-layout'
import { 
  Search, 
  Shield, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Eye,
  User,
  CreditCard,
  ImageIcon
} from 'lucide-react'
import { formatDate } from '@/lib/utils'
import { toast } from '@/hooks/use-toast'
import { KYCImage } from '@/components/admin/kyc-image'

interface KYCDocument {
  id: string
  user_id: string
  aadhaar_url: string | null
  selfie_url: string | null
  aadhaar_number: string | null
  verification_status: string
  rejection_reason: string | null
  verified_at: string | null
  verified_by: string | null
  created_at: string
  updated_at: string
  profiles: {
    full_name: string | null
    email: string | null
    phone: string | null
  }
}

type TabType = 'pending' | 'in_review' | 'verified' | 'rejected'

export default function KYCPage() {
  const [documents, setDocuments] = useState<KYCDocument[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<TabType>('pending')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedDoc, setSelectedDoc] = useState<KYCDocument | null>(null)
  const [rejectionReason, setRejectionReason] = useState('')

  useEffect(() => {
    fetchKYCDocuments()
  }, [activeTab])

  const fetchKYCDocuments = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/kyc?status=${activeTab}`)
      if (response.ok) {
        const data = await response.json()
        setDocuments(data.documents)
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch KYC documents",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch KYC documents",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleVerificationAction = async (documentId: string, action: 'approve' | 'reject', reason?: string) => {
    try {
      const response = await fetch(`/api/admin/kyc/${documentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          rejection_reason: reason
        }),
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: `KYC ${action === 'approve' ? 'approved' : 'rejected'} successfully`,
        })
        fetchKYCDocuments()
        setSelectedDoc(null)
        setRejectionReason('')
      } else {
        const errorData = await response.json()
        throw new Error(errorData.message || `Failed to ${action} KYC`)
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : `Failed to ${action} KYC`,
        variant: "destructive",
      })
    }
  }

  const openDocumentViewer = (doc: KYCDocument) => {
    setSelectedDoc(doc)
  }

  const closeDocumentViewer = () => {
    setSelectedDoc(null)
    setRejectionReason('')
  }

  const filteredDocuments = documents.filter(doc => {
    if (!searchTerm.trim()) return true

    const searchLower = searchTerm.toLowerCase()
    return (
      doc.profiles?.full_name?.toLowerCase().includes(searchLower) ||
      doc.profiles?.email?.toLowerCase().includes(searchLower) ||
      doc.profiles?.phone?.includes(searchTerm) ||
      doc.aadhaar_number?.includes(searchTerm)
    )
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'verified':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-600" />
      case 'in_review':
        return <Clock className="h-5 w-5 text-blue-600" />
      default:
        return <Clock className="h-5 w-5 text-yellow-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
      in_review: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
      verified: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      rejected: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    }

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colors[status as keyof typeof colors]}`}>
        {getStatusIcon(status)}
        <span className="ml-1 capitalize">{status}</span>
      </span>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold">KYC Verification</h1>
          <p className="text-muted-foreground">
            Review and approve seller identity documents
          </p>
        </div>

        {/* Tabs */}
        <div className="flex gap-1 bg-muted p-1 rounded-lg w-fit">
          {(['pending', 'in_review', 'verified', 'rejected'] as TabType[]).map((tab) => (
            <Button
              key={tab}
              variant={activeTab === tab ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveTab(tab)}
              className="capitalize"
            >
              {tab === 'in_review' ? 'In Review' : tab}
              <span className="ml-2 bg-background/20 text-xs px-1.5 py-0.5 rounded">
                {documents.length}
              </span>
            </Button>
          ))}
        </div>

        {/* Search */}
        <Card>
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name, email, phone, or Aadhaar number..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Documents List */}
        {loading ? (
          <div className="space-y-4">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="h-4 bg-muted rounded mb-2"></div>
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredDocuments.length > 0 ? (
          <div className="space-y-4">
            {filteredDocuments.map((doc) => (
              <Card key={doc.id}>
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="text-lg font-semibold">
                            {doc.profiles?.full_name || 'Unnamed User'}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            {doc.profiles?.email}
                          </p>
                        </div>
                        {getStatusBadge(doc.verification_status)}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span>User ID: {doc.user_id.slice(0, 8)}...</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CreditCard className="h-4 w-4 text-muted-foreground" />
                          <span>
                            {doc.aadhaar_number 
                              ? `****-****-${doc.aadhaar_number.slice(-4)}`
                              : 'No Aadhaar number'
                            }
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <ImageIcon className="h-4 w-4 text-muted-foreground" />
                          <span>
                            {doc.aadhaar_url && doc.selfie_url 
                              ? 'Both documents uploaded'
                              : 'Incomplete documents'
                            }
                          </span>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          <p>Submitted: {formatDate(doc.created_at)}</p>
                          {doc.verified_at && (
                            <p>Verified: {formatDate(doc.verified_at)}</p>
                          )}
                        </div>
                      </div>

                      {doc.rejection_reason && (
                        <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-md">
                          <p className="text-sm text-red-800 dark:text-red-400">
                            <strong>Rejection Reason:</strong> {doc.rejection_reason}
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col sm:flex-row gap-2 lg:ml-6">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openDocumentViewer(doc)}
                        className="w-full sm:w-auto"
                      >
                        <Eye className="mr-2 h-4 w-4" />
                        Review Documents
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Shield className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No KYC Documents Found</h3>
              <p className="text-muted-foreground text-center">
                {searchTerm 
                  ? "No documents match your search." 
                  : `No ${activeTab} KYC documents at the moment.`
                }
              </p>
            </CardContent>
          </Card>
        )}

        {/* Document Viewer Modal */}
        {selectedDoc && (
          <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
            <div className="bg-background rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h2 className="text-xl font-semibold">
                      KYC Documents - {selectedDoc.profiles?.full_name}
                    </h2>
                    <p className="text-muted-foreground">
                      {selectedDoc.profiles?.email}
                    </p>
                  </div>
                  <Button variant="outline" onClick={closeDocumentViewer}>
                    Close
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  {/* Aadhaar Document */}
                  <div>
                    <h3 className="font-medium mb-3">Aadhaar Card</h3>
                    {selectedDoc.aadhaar_url ? (
                      <KYCImage
                        path={selectedDoc.aadhaar_url}
                        alt="Aadhaar Card"
                        width={400}
                        height={250}
                      />
                    ) : (
                      <div className="border-2 border-dashed border-muted rounded-lg p-8 text-center">
                        <ImageIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                        <p className="text-muted-foreground">No Aadhaar document uploaded</p>
                      </div>
                    )}
                  </div>

                  {/* Selfie */}
                  <div>
                    <h3 className="font-medium mb-3">Selfie</h3>
                    {selectedDoc.selfie_url ? (
                      <KYCImage
                        path={selectedDoc.selfie_url}
                        alt="Selfie"
                        width={400}
                        height={250}
                      />
                    ) : (
                      <div className="border-2 border-dashed border-muted rounded-lg p-8 text-center">
                        <ImageIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                        <p className="text-muted-foreground">No selfie uploaded</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Aadhaar Number */}
                {selectedDoc.aadhaar_number && (
                  <div className="mb-6 p-4 bg-muted rounded-lg">
                    <h3 className="font-medium mb-2">Aadhaar Number</h3>
                    <p className="font-mono text-lg">{selectedDoc.aadhaar_number}</p>
                  </div>
                )}

                {/* Actions */}
                {(selectedDoc.verification_status === 'pending' || selectedDoc.verification_status === 'in_review') && (
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Rejection Reason (if rejecting)</label>
                      <Input
                        placeholder="Enter reason for rejection..."
                        value={rejectionReason}
                        onChange={(e) => setRejectionReason(e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button
                        onClick={() => handleVerificationAction(selectedDoc.id, 'approve')}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Approve KYC
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={() => handleVerificationAction(selectedDoc.id, 'reject', rejectionReason)}
                        disabled={!rejectionReason.trim()}
                      >
                        <XCircle className="mr-2 h-4 w-4" />
                        Reject KYC
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}