"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/kyc/page",{

/***/ "(app-pages-browser)/./src/app/admin/kyc/page.tsx":
/*!************************************!*\
  !*** ./src/app/admin/kyc/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ KYCPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_layout_admin_layout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/admin-layout */ \"(app-pages-browser)/./src/components/layout/admin-layout.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,CreditCard,Eye,ImageIcon,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_admin_kyc_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/admin/kyc-image */ \"(app-pages-browser)/./src/components/admin/kyc-image.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction KYCPage() {\n    var _selectedDoc_profiles, _selectedDoc_profiles1;\n    _s();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('pending');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedDoc, setSelectedDoc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rejectionReason, setRejectionReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"KYCPage.useEffect\": ()=>{\n            fetchKYCDocuments();\n        }\n    }[\"KYCPage.useEffect\"], [\n        activeTab\n    ]);\n    const fetchKYCDocuments = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/admin/kyc?status=\".concat(activeTab));\n            if (response.ok) {\n                const data = await response.json();\n                setDocuments(data.documents);\n            } else {\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                    title: \"Error\",\n                    description: \"Failed to fetch KYC documents\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to fetch KYC documents\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleVerificationAction = async (documentId, action, reason)=>{\n        try {\n            const response = await fetch(\"/api/admin/kyc/\".concat(documentId), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action,\n                    rejection_reason: reason\n                })\n            });\n            if (response.ok) {\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                    title: \"Success\",\n                    description: \"KYC \".concat(action === 'approve' ? 'approved' : 'rejected', \" successfully\")\n                });\n                fetchKYCDocuments();\n                setSelectedDoc(null);\n                setRejectionReason('');\n            } else {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Failed to \".concat(action, \" KYC\"));\n            }\n        } catch (error) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: error instanceof Error ? error.message : \"Failed to \".concat(action, \" KYC\"),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const openDocumentViewer = (doc)=>{\n        setSelectedDoc(doc);\n    };\n    const closeDocumentViewer = ()=>{\n        setSelectedDoc(null);\n        setRejectionReason('');\n    };\n    const filteredDocuments = documents.filter((doc)=>{\n        var _doc_profiles_full_name, _doc_profiles, _doc_profiles_email, _doc_profiles1, _doc_profiles_phone, _doc_profiles2, _doc_aadhaar_number;\n        return ((_doc_profiles = doc.profiles) === null || _doc_profiles === void 0 ? void 0 : (_doc_profiles_full_name = _doc_profiles.full_name) === null || _doc_profiles_full_name === void 0 ? void 0 : _doc_profiles_full_name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_doc_profiles1 = doc.profiles) === null || _doc_profiles1 === void 0 ? void 0 : (_doc_profiles_email = _doc_profiles1.email) === null || _doc_profiles_email === void 0 ? void 0 : _doc_profiles_email.toLowerCase().includes(searchTerm.toLowerCase())) || ((_doc_profiles2 = doc.profiles) === null || _doc_profiles2 === void 0 ? void 0 : (_doc_profiles_phone = _doc_profiles2.phone) === null || _doc_profiles_phone === void 0 ? void 0 : _doc_profiles_phone.includes(searchTerm)) || ((_doc_aadhaar_number = doc.aadhaar_number) === null || _doc_aadhaar_number === void 0 ? void 0 : _doc_aadhaar_number.includes(searchTerm));\n    });\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'verified':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-5 w-5 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 16\n                }, this);\n            case 'rejected':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-5 w-5 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 16\n                }, this);\n            case 'in_review':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-5 w-5 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const colors = {\n            pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',\n            in_review: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',\n            verified: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',\n            rejected: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(colors[status]),\n            children: [\n                getStatusIcon(status),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-1 capitalize\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_admin_layout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold\",\n                            children: \"KYC Verification\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Review and approve seller identity documents\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1 bg-muted p-1 rounded-lg w-fit\",\n                    children: [\n                        'pending',\n                        'in_review',\n                        'verified',\n                        'rejected'\n                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: activeTab === tab ? 'default' : 'ghost',\n                            size: \"sm\",\n                            onClick: ()=>setActiveTab(tab),\n                            className: \"capitalize\",\n                            children: [\n                                tab,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 bg-background/20 text-xs px-1.5 py-0.5 rounded\",\n                                    children: documents.length\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, tab, true, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"absolute left-3 top-3 h-4 w-4 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    placeholder: \"Search by name, email, phone, or Aadhaar number...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"pl-10\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, this),\n                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        ...Array(6)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"animate-pulse\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-3/4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 17\n                            }, this)\n                        }, i, false, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 11\n                }, this) : filteredDocuments.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: filteredDocuments.map((doc)=>{\n                        var _doc_profiles, _doc_profiles1;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: ((_doc_profiles = doc.profiles) === null || _doc_profiles === void 0 ? void 0 : _doc_profiles.full_name) || 'Unnamed User'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: (_doc_profiles1 = doc.profiles) === null || _doc_profiles1 === void 0 ? void 0 : _doc_profiles1.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        getStatusBadge(doc.verification_status)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"User ID: \",\n                                                                        doc.user_id.slice(0, 8),\n                                                                        \"...\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 240,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: doc.aadhaar_number ? \"****-****-\".concat(doc.aadhaar_number.slice(-4)) : 'No Aadhaar number'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 241,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: doc.aadhaar_url && doc.selfie_url ? 'Both documents uploaded' : 'Incomplete documents'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Submitted: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(doc.created_at)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 258,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                doc.verified_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Verified: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(doc.verified_at)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 23\n                                                }, this),\n                                                doc.rejection_reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-md\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-800 dark:text-red-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Rejection Reason:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            \" \",\n                                                            doc.rejection_reason\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-2 lg:ml-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>openDocumentViewer(doc),\n                                                className: \"w-full sm:w-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"Review Documents\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 17\n                            }, this)\n                        }, doc.id, false, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"flex flex-col items-center justify-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-12 w-12 text-muted-foreground mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"No KYC Documents Found\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-center\",\n                                children: searchTerm ? \"No documents match your search.\" : \"No \".concat(activeTab, \" KYC documents at the moment.\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 11\n                }, this),\n                selectedDoc && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-background rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold\",\n                                                    children: [\n                                                        \"KYC Documents - \",\n                                                        (_selectedDoc_profiles = selectedDoc.profiles) === null || _selectedDoc_profiles === void 0 ? void 0 : _selectedDoc_profiles.full_name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: (_selectedDoc_profiles1 = selectedDoc.profiles) === null || _selectedDoc_profiles1 === void 0 ? void 0 : _selectedDoc_profiles1.email\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            onClick: closeDocumentViewer,\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium mb-3\",\n                                                    children: \"Aadhaar Card\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 21\n                                                }, this),\n                                                selectedDoc.aadhaar_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_kyc_image__WEBPACK_IMPORTED_MODULE_8__.KYCImage, {\n                                                    path: selectedDoc.aadhaar_url,\n                                                    alt: \"Aadhaar Card\",\n                                                    width: 400,\n                                                    height: 250\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-2 border-dashed border-muted rounded-lg p-8 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-8 w-8 text-muted-foreground mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"No Aadhaar document uploaded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium mb-3\",\n                                                    children: \"Selfie\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 21\n                                                }, this),\n                                                selectedDoc.selfie_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_kyc_image__WEBPACK_IMPORTED_MODULE_8__.KYCImage, {\n                                                    path: selectedDoc.selfie_url,\n                                                    alt: \"Selfie\",\n                                                    width: 400,\n                                                    height: 250\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-2 border-dashed border-muted rounded-lg p-8 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-8 w-8 text-muted-foreground mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"No selfie uploaded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 17\n                                }, this),\n                                selectedDoc.aadhaar_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 p-4 bg-muted rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium mb-2\",\n                                            children: \"Aadhaar Number\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-mono text-lg\",\n                                            children: selectedDoc.aadhaar_number\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 19\n                                }, this),\n                                selectedDoc.verification_status === 'pending' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Rejection Reason (if rejecting)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    placeholder: \"Enter reason for rejection...\",\n                                                    value: rejectionReason,\n                                                    onChange: (e)=>setRejectionReason(e.target.value),\n                                                    className: \"mt-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: ()=>handleVerificationAction(selectedDoc.id, 'approve'),\n                                                    className: \"bg-green-600 hover:bg-green-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Approve KYC\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"destructive\",\n                                                    onClick: ()=>handleVerificationAction(selectedDoc.id, 'reject', rejectionReason),\n                                                    disabled: !rejectionReason.trim(),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_CreditCard_Eye_ImageIcon_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Reject KYC\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n            lineNumber: 161,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\admin-panel\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_s(KYCPage, \"eHaR2xYH0VERwt/2PtPfhXyL5hw=\");\n_c = KYCPage;\nvar _c;\n$RefreshReg$(_c, \"KYCPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/kyc/page.tsx\n"));

/***/ })

});