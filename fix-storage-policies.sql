-- Fix Supabase Storage Policies for Product Images
-- Run this in your Supabase SQL Editor

-- 1. First, create the products bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'products',
  'products',
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
) ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 5242880,
  allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'];

-- 2. Drop existing policies if they exist
DROP POLICY IF EXISTS "Public read access for products" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated upload for products" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated delete for products" ON storage.objects;

-- 3. Create new policies for products bucket

-- Allow public read access to products bucket
CREATE POLICY "Public read access for products" ON storage.objects
  FOR SELECT USING (bucket_id = 'products');

-- Allow authenticated users to upload to products bucket
CREATE POLICY "Authenticated upload for products" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'products' 
    AND auth.role() = 'authenticated'
  );

-- Allow authenticated users to update files in products bucket
CREATE POLICY "Authenticated update for products" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'products' 
    AND auth.role() = 'authenticated'
  );

-- Allow authenticated users to delete files in products bucket
CREATE POLICY "Authenticated delete for products" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'products' 
    AND auth.role() = 'authenticated'
  );

-- 4. Also create policies for admin users (service role)
CREATE POLICY "Service role can manage products" ON storage.objects
  FOR ALL USING (
    bucket_id = 'products'
    AND auth.role() = 'service_role'
  );

-- 5. Create categories bucket and policies
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'categories',
  'categories',
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
) ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 5242880,
  allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'];

-- Drop existing policies for categories if they exist
DROP POLICY IF EXISTS "Public read access for categories" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated upload for categories" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated delete for categories" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated update for categories" ON storage.objects;
DROP POLICY IF EXISTS "Service role can manage categories" ON storage.objects;

-- Create policies for categories bucket
CREATE POLICY "Public read access for categories" ON storage.objects
  FOR SELECT USING (bucket_id = 'categories');

CREATE POLICY "Authenticated upload for categories" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'categories'
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Authenticated update for categories" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'categories'
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Authenticated delete for categories" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'categories'
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Service role can manage categories" ON storage.objects
  FOR ALL USING (
    bucket_id = 'categories'
    AND auth.role() = 'service_role'
  );

-- 6. Create KYC bucket and policies (PRIVATE - for sensitive documents)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'kyc',
  'kyc',
  false, -- PRIVATE bucket for sensitive documents
  10485760, -- 10MB limit for documents
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'application/pdf']
) ON CONFLICT (id) DO UPDATE SET
  public = false,
  file_size_limit = 10485760,
  allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/webp', 'application/pdf'];

-- Drop existing policies for KYC if they exist
DROP POLICY IF EXISTS "Users can manage own KYC files" ON storage.objects;
DROP POLICY IF EXISTS "Admins can view all KYC files" ON storage.objects;
DROP POLICY IF EXISTS "Service role can manage KYC" ON storage.objects;

-- Create policies for KYC bucket (PRIVATE)
-- Users can only access their own KYC files (folder structure: kyc/user_id/filename)
CREATE POLICY "Users can manage own KYC files" ON storage.objects
  FOR ALL USING (
    bucket_id = 'kyc'
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- Admins can view all KYC files
CREATE POLICY "Admins can view all KYC files" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'kyc'
    AND EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid()
      AND email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
    )
  );

-- Service role can manage all KYC files
CREATE POLICY "Service role can manage KYC" ON storage.objects
  FOR ALL USING (
    bucket_id = 'kyc'
    AND auth.role() = 'service_role'
  );

-- 7. Create banners bucket and policies (PUBLIC)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'banners',
  'banners',
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
) ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 5242880,
  allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'];

-- Drop existing policies for banners if they exist
DROP POLICY IF EXISTS "Public read access for banners" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated upload for banners" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated delete for banners" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated update for banners" ON storage.objects;
DROP POLICY IF EXISTS "Service role can manage banners" ON storage.objects;

-- Create policies for banners bucket
CREATE POLICY "Public read access for banners" ON storage.objects
  FOR SELECT USING (bucket_id = 'banners');

CREATE POLICY "Authenticated upload for banners" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'banners'
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Authenticated update for banners" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'banners'
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Authenticated delete for banners" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'banners'
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Service role can manage banners" ON storage.objects
  FOR ALL USING (
    bucket_id = 'banners'
    AND auth.role() = 'service_role'
  );

-- 8. Verify all buckets exist and policies are applied
-- Verify all buckets exist
SELECT
  id,
  name,
  public,
  file_size_limit,
  allowed_mime_types
FROM storage.buckets
WHERE id IN ('products', 'categories', 'kyc', 'banners')
ORDER BY id;

-- Check all storage policies
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies
WHERE tablename = 'objects'
AND schemaname = 'storage'
ORDER BY policyname;
